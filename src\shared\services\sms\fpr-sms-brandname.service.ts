import { Injectable, Logger, Inject } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { firstValueFrom } from 'rxjs';
import Redis from 'ioredis';

// TypeScript interfaces tương ứng với Java DTOs
export interface AccessTokenRequest {
  grant_type: string;
  client_id: string;
  client_secret: string;
  scope: string;
  session_id: string;
}

export interface AccessTokenResponse {
  access_token: string;
  expires_in: number;
  token_type: string;
  scope: string;
  error?: number;
  error_description?: string;
}

export interface OtpRequest {
  access_token: string;
  session_id: string;
  BrandName: string;
  Phone: string;
  Message: string;
}

export interface OtpResponse {
  MessageId: string;
  BrandName: string;
  Phone: string;
  Message: string;
  PartnerId: string;
  Telco: string;
  Status: string;
  ErrorMessage?: string;
}

export interface CampaignRequest {
  access_token: string;
  session_id: string;
  CampaignName: string;
  BrandName: string;
  Message: string;
  ScheduleTime: string; // Format: "yyyy-MM-dd HH:mm"
  Quota: number;
}

export interface CampaignResponse {
  CampaignCode: string;
  CampaignName: string;
  BrandName: string;
  Message: string;
  ScheduleTime: string;
  Quota: number;
  Status: string;
}

export interface AdsRequest {
  access_token: string;
  session_id: string;
  CampaignCode: string;
  PhoneList: string; // Danh sách số điện thoại phân cách bằng dấu phẩy
}

export interface AdsResponse {
  CampaignCode: string;
  TotalSent: number;
  SuccessCount: number;
  FailureCount: number;
  Details: Array<{
    Phone: string;
    Status: string;
    MessageId?: string;
    ErrorCode?: string;
    ErrorMessage?: string;
  }>;
}

export interface DetailsCampaignRequest {
  access_token: string;
  session_id: string;
  CampaignCode: string;
}

export interface DetailsCampaignResponse {
  CampaignCode: string;
  CampaignName: string;
  BrandName: string;
  Message: string;
  ScheduleTime: string;
  Quota: number;
  Status: string;
  TotalSent: number;
  SuccessCount: number;
  FailureCount: number;
  CreatedAt: string;
  UpdatedAt: string;
}

export interface DetailsStatusCampaignRequest {
  access_token: string;
  session_id: string;
  CampaignCode: string;
}

export interface CancelAdsRequest {
  access_token: string;
  session_id: string;
  CampaignCode: string;
}

export interface CancelAdsResponse {
  CampaignCode: string;
  Status: string;
  Message: string;
}

export interface DlrAdsRecheckRequest {
  access_token: string;
  session_id: string;
  FromDate: string; // Format: "yyyy-MM-dd"
  ToDate: string;   // Format: "yyyy-MM-dd"
}

export interface DlrAdsRecheckResponse {
  Campaigns: Array<{
    CampaignCode: string;
    CampaignName: string;
    TotalSent: number;
    ReceivedDlr: number;
    PendingDlr: number;
  }>;
}

export interface FprSmsConfig {
  apiUrl: string;
  clientId: string;
  clientSecret: string;
  brandName: string;
  sessionId?: string;
}

@Injectable()
export class FprSmsBrandnameService {
  private readonly logger = new Logger(FprSmsBrandnameService.name);
  private readonly REDIS_TOKEN_KEY = 'fpt_sms_access_token';
  private readonly TOKEN_EXPIRY_SECONDS = 20 * 60; // 20 phút

  constructor(
    private readonly httpService: HttpService,
    private readonly config: FprSmsConfig,
    @Inject(Redis) private readonly redis: Redis
  ) {}

  /**
   * Tạo session ID ngẫu nhiên (tối đa 32 ký tự)
   */
  private generateSessionId(): string {
    // Tạo chuỗi ngẫu nhiên từ timestamp và random string
    const timestamp = Date.now().toString(36);
    const randomStr = Math.random().toString(36).substring(2);
    const sessionId = `${timestamp}_${randomStr}`;

    // Đảm bảo không vượt quá 32 ký tự
    return sessionId.length > 32 ? sessionId.substring(0, 32) : sessionId;
  }

  /**
   * Kiểm tra và làm mới access token nếu cần từ Redis
   */
  private async ensureValidToken(): Promise<string> {
    try {
      // Kiểm tra token trong Redis
      const cachedToken = await this.redis.get(this.REDIS_TOKEN_KEY);

      if (cachedToken) {
        this.logger.debug('Using cached access token from Redis');
        return cachedToken;
      }

      // Nếu không có token trong Redis, lấy token mới
      this.logger.debug('No cached token found, getting new access token');
      const tokenResponse = await this.getAccessToken();
      return tokenResponse.access_token;
    } catch (error) {
      this.logger.error('Error ensuring valid token', error);
      throw error;
    }
  }

  /**
   * API Lấy Access Token và lưu vào Redis
   */
  async getAccessToken(): Promise<AccessTokenResponse> {
    try {
      const sessionId = this.generateSessionId();
      const request: AccessTokenRequest = {
        grant_type: 'client_credentials',
        client_id: this.config.clientId,
        client_secret: this.config.clientSecret,
        scope: 'send_brandname_otp send_brandname_ads',
        session_id: sessionId
      };

      this.logger.debug(`Getting access token with session_id: ${sessionId}`);

      const response = await firstValueFrom(
        this.httpService.post<AccessTokenResponse>(
          `${this.config.apiUrl}/oauth2/token`,
          request,
          {
            headers: {
              'Content-Type': 'application/json'
            }
          }
        )
      );

      if (response.data.error) {
        throw new Error(`Token error: ${response.data.error_description}`);
      }

      // Lưu token vào Redis với thời gian sống 20 phút
      await this.redis.setex(
        this.REDIS_TOKEN_KEY,
        this.TOKEN_EXPIRY_SECONDS,
        response.data.access_token
      );

      this.logger.log(`Access token retrieved and cached in Redis for ${this.TOKEN_EXPIRY_SECONDS} seconds`);
      return response.data;
    } catch (error) {
      this.logger.error('Failed to get access token', error);
      throw error;
    }
  }

  /**
   * API Gửi Tin Nhắn Brandname OTP
   */
  async sendOtp(request: Omit<OtpRequest, 'access_token' | 'session_id'>): Promise<OtpResponse> {
    try {
      const accessToken = await this.ensureValidToken();
      const sessionId = this.generateSessionId();

      const otpRequest: OtpRequest = {
        access_token: accessToken,
        session_id: sessionId,
        ...request
      };

      this.logger.debug(`Sending OTP with session_id: ${sessionId}`);

      const response = await firstValueFrom(
        this.httpService.post<OtpResponse>(
          `${this.config.apiUrl}/api/push-brandname-otp`,
          otpRequest,
          {
            headers: {
              'Content-Type': 'application/json'
            }
          }
        )
      );

      this.logger.log(`OTP sent successfully to ${request.Phone}`);
      return response.data;
    } catch (error) {
      this.logger.error('Failed to send OTP', error);
      throw error;
    }
  }

  /**
   * API Tạo Campaign Quảng Cáo
   */
  async createCampaign(request: Omit<CampaignRequest, 'access_token' | 'session_id'>): Promise<CampaignResponse> {
    try {
      const accessToken = await this.ensureValidToken();
      const sessionId = this.generateSessionId();

      const campaignRequest: CampaignRequest = {
        access_token: accessToken,
        session_id: sessionId,
        ...request
      };

      this.logger.debug(`Creating campaign with session_id: ${sessionId}`);

      const response = await firstValueFrom(
        this.httpService.post<CampaignResponse>(
          `${this.config.apiUrl}/api/create-campaign`,
          campaignRequest,
          {
            headers: {
              'Content-Type': 'application/json'
            }
          }
        )
      );

      this.logger.log(`Campaign created successfully: ${response.data.CampaignCode}`);
      return response.data;
    } catch (error) {
      this.logger.error('Failed to create campaign', error);
      throw error;
    }
  }

  /**
   * API Xem Chi Tiết Campaign Quảng Cáo
   */
  async detailsCampaign(request: Omit<DetailsCampaignRequest, 'access_token' | 'session_id'>): Promise<DetailsCampaignResponse> {
    try {
      const accessToken = await this.ensureValidToken();
      const sessionId = this.generateSessionId();

      const detailsRequest: DetailsCampaignRequest = {
        access_token: accessToken,
        session_id: sessionId,
        ...request
      };

      this.logger.debug(`Getting campaign details with session_id: ${sessionId}`);

      const response = await firstValueFrom(
        this.httpService.post<DetailsCampaignResponse>(
          `${this.config.apiUrl}/api/detail-ads`,
          detailsRequest,
          {
            headers: {
              'Content-Type': 'application/json'
            }
          }
        )
      );

      this.logger.log(`Campaign details retrieved: ${request.CampaignCode}`);
      return response.data;
    } catch (error) {
      this.logger.error('Failed to get campaign details', error);
      throw error;
    }
  }

  /**
   * API Lấy Trạng Thái Của Từng Tin Quảng Cáo
   */
  async detailsStatusCampaign(request: Omit<DetailsStatusCampaignRequest, 'access_token' | 'session_id'>): Promise<ArrayBuffer> {
    try {
      const accessToken = await this.ensureValidToken();
      const sessionId = this.generateSessionId();

      const statusRequest: DetailsStatusCampaignRequest = {
        access_token: accessToken,
        session_id: sessionId,
        ...request
      };

      this.logger.debug(`Getting campaign status with session_id: ${sessionId}`);

      const response = await firstValueFrom(
        this.httpService.post<ArrayBuffer>(
          `${this.config.apiUrl}/api/dlr-ads`,
          statusRequest,
          {
            headers: {
              'Content-Type': 'application/json'
            },
            responseType: 'arraybuffer'
          }
        )
      );

      this.logger.log(`Campaign status details retrieved: ${request.CampaignCode}`);
      return response.data;
    } catch (error) {
      this.logger.error('Failed to get campaign status details', error);
      throw error;
    }
  }

  /**
   * API Gửi Tin Nhắn Quảng Cáo
   */
  async sendAds(request: Omit<AdsRequest, 'access_token' | 'session_id'>): Promise<AdsResponse> {
    try {
      const accessToken = await this.ensureValidToken();
      const sessionId = this.generateSessionId();

      const adsRequest: AdsRequest = {
        access_token: accessToken,
        session_id: sessionId,
        ...request
      };

      this.logger.debug(`Sending ads with session_id: ${sessionId}`);

      const response = await firstValueFrom(
        this.httpService.post<AdsResponse>(
          `${this.config.apiUrl}/api/push-brandname-ads`,
          adsRequest,
          {
            headers: {
              'Content-Type': 'application/json'
            }
          }
        )
      );

      this.logger.log(`Ads sent successfully for campaign: ${request.CampaignCode}`);
      return response.data;
    } catch (error) {
      this.logger.error('Failed to send ads', error);
      throw error;
    }
  }

  /**
   * API Hủy Tin Nhắn Quảng Cáo
   */
  async cancelAds(request: Omit<CancelAdsRequest, 'access_token' | 'session_id'>): Promise<CancelAdsResponse> {
    try {
      const accessToken = await this.ensureValidToken();
      const sessionId = this.generateSessionId();

      const cancelRequest: CancelAdsRequest = {
        access_token: accessToken,
        session_id: sessionId,
        ...request
      };

      this.logger.debug(`Cancelling ads with session_id: ${sessionId}`);

      const response = await firstValueFrom(
        this.httpService.post<CancelAdsResponse>(
          `${this.config.apiUrl}/api/cancel-ads`,
          cancelRequest,
          {
            headers: {
              'Content-Type': 'application/json'
            }
          }
        )
      );

      this.logger.log(`Campaign cancelled successfully: ${request.CampaignCode}`);
      return response.data;
    } catch (error) {
      this.logger.error('Failed to cancel ads', error);
      throw error;
    }
  }

  /**
   * API Lấy danh sách campaign QC chưa nhận đủ DLR
   */
  async getDlrAdsRecheck(request: Omit<DlrAdsRecheckRequest, 'access_token' | 'session_id'>): Promise<DlrAdsRecheckResponse> {
    try {
      const accessToken = await this.ensureValidToken();
      const sessionId = this.generateSessionId();

      const recheckRequest: DlrAdsRecheckRequest = {
        access_token: accessToken,
        session_id: sessionId,
        ...request
      };

      this.logger.debug(`Getting DLR recheck with session_id: ${sessionId}`);

      const response = await firstValueFrom(
        this.httpService.post<DlrAdsRecheckResponse>(
          `${this.config.apiUrl}/dlr-ads-recheck`,
          recheckRequest,
          {
            headers: {
              'Content-Type': 'application/json'
            }
          }
        )
      );

      this.logger.log(`DLR recheck data retrieved for period: ${request.FromDate} to ${request.ToDate}`);
      return response.data;
    } catch (error) {
      this.logger.error('Failed to get DLR recheck data', error);
      throw error;
    }
  }

  /**
   * Utility method: Gửi OTP với template mặc định
   */
  async sendOtpWithTemplate(phone: string, otpCode: string, template?: string): Promise<OtpResponse> {
    const message = template
      ? template.replace('{code}', otpCode)
      : `Mã xác thực của bạn là: ${otpCode}. Vui lòng không chia sẻ mã này với ai khác.`;

    return this.sendOtp({
      BrandName: this.config.brandName,
      Phone: phone,
      Message: message
    });
  }

  /**
   * Utility method: Tạo và gửi campaign trong một lần
   */
  async createAndSendCampaign(
    campaignName: string,
    message: string,
    phoneList: string[],
    scheduleTime?: string,
    quota?: number
  ): Promise<{ campaign: CampaignResponse; ads: AdsResponse }> {
    try {
      // Tạo campaign
      const campaign = await this.createCampaign({
        CampaignName: campaignName,
        BrandName: this.config.brandName,
        Message: message,
        ScheduleTime: scheduleTime || new Date().toISOString().slice(0, 16).replace('T', ' '),
        Quota: quota || phoneList.length
      });

      // Gửi ads
      const ads = await this.sendAds({
        CampaignCode: campaign.CampaignCode,
        PhoneList: phoneList.join(',')
      });

      return { campaign, ads };
    } catch (error) {
      this.logger.error('Failed to create and send campaign', error);
      throw error;
    }
  }

  /**
   * Utility method: Kiểm tra trạng thái kết nối
   */
  async testConnection(): Promise<boolean> {
    try {
      await this.getAccessToken();
      return true;
    } catch (error) {
      this.logger.error('Connection test failed', error);
      return false;
    }
  }

  /**
   * Utility method: Xóa access token khỏi Redis
   */
  async clearCachedToken(): Promise<void> {
    try {
      await this.redis.del(this.REDIS_TOKEN_KEY);
      this.logger.log('Cached access token cleared from Redis');
    } catch (error) {
      this.logger.error('Failed to clear cached token', error);
      throw error;
    }
  }

  /**
   * Utility method: Kiểm tra token có tồn tại trong Redis không
   */
  async hasValidCachedToken(): Promise<boolean> {
    try {
      const cachedToken = await this.redis.get(this.REDIS_TOKEN_KEY);
      return !!cachedToken;
    } catch (error) {
      this.logger.error('Failed to check cached token', error);
      return false;
    }
  }
}