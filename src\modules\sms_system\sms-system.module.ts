import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { BullModule } from '@nestjs/bullmq';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { HttpModule, HttpService } from '@nestjs/axios';
import Redis from 'ioredis';
import { env } from '../../config';
import { QueueName } from '../../queue';
import { AdminTemplateSms } from './entities/admin-template-sms.entity';
import { SmsSystemService } from './sms-system.service';
import { SmsSystemProcessor } from './sms-system.processor';
import { SmsTemplateService } from './services/sms-template.service';
import { SmsEncryptionService } from './services/sms-encryption.service';
import { FprSmsBrandnameService, FprSmsConfig } from '../../shared/services/sms/fpr-sms-brandname.service';

/**
 * Module xử lý hệ thống SMS
 */
@Module({
  imports: [
    ConfigModule,
    TypeOrmModule.forFeature([AdminTemplateSms]),
    BullModule.registerQueue({
      name: QueueName.SMS,
    }),
    HttpModule.register({
      timeout: 30000, // 30 seconds timeout
      maxRedirects: 5,
    }),
  ],
  providers: [
    SmsSystemService,
    SmsSystemProcessor,
    SmsTemplateService,
    SmsEncryptionService,
    {
      provide: Redis,
      useFactory: () => {
        return new Redis(env.external.REDIS_URL);
      },
    },
    {
      provide: FprSmsBrandnameService,
      useFactory: (httpService: HttpService, configService: ConfigService, redis: Redis) => {
        const config: FprSmsConfig = {
          apiUrl: configService.get<string>('FPT_SMS_API_URL') || 'https://api.fpt.net/api',
          clientId: configService.get<string>('FPT_SMS_CLIENT_ID') || '',
          clientSecret: configService.get<string>('FPT_SMS_CLIENT_SECRET') || '',
          brandName: configService.get<string>('FPT_SMS_BRANDNAME') || 'REDAI',
        };
        return new FprSmsBrandnameService(httpService, config, redis);
      },
      inject: [HttpService, ConfigService, Redis],
    },
  ],
  exports: [SmsSystemService, SmsTemplateService, SmsEncryptionService],
})
export class SmsSystemModule {}
